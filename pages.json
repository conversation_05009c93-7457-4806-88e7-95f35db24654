{
  "pages": [
    {
      "path": "pages/home/<USER>",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "",
        "app-plus": {
          "scrollIndicator": "none",
          //app端隐藏滚动条
          "bounce": "none",
          "titleNView": {
            "padding-right": "6px",
            "buttons": [
              {
                "type": "menu",
                "index": "0"
              }
            ]
          }
        }
      }
    },
    {
      "path": "pages/groupon/index",
      "style": {
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/tabbar/home/<USER>",
      "style": {
        "navigationBarTitleText": "首页",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/tabbar/products/index",
      "style": {
        "navigationBarTitleText": "全部产品"
      }
    },
    {
      "path": "pages/tabbar/coupons/index",
      "style": {
        "navigationBarTitleText": "优惠券"
      }
    },
    {
      "path": "pages/tabbar/user/index",
      "style": {
        "navigationBarTitleText": "我的"
      }
    },
    {
      "path": "pages/appoint/index"
      //档期选择
    },
    {
      "path": "pages/order/order-pay-result/receipt"
      //点金计划 订单支付成功小票页面
    },
    {
      "path": "pages/form/index"
      //图文表单页面
    },
    {
      "path": "pages/img-share/index"
      //客片分享页
    },
    {
      "path": "pages/img-share/bind"
      //客片分享绑定页
    },
    {
      "path": "pages/cooperation/index"
      //招商合作页
    },
    {
      "path": "pages/login/index"
    },
    {
      "path": "pages/login/wechat-auth",
      "style": {
        "navigationBarTitleText": "微信授权登录"
      }
    },
    {
      "path": "pages/login/user-agreement",
      "style": {
        "navigationBarTitleText": "用户协议"
      }
    },
    {
      "path": "pages/login/privacy-policy",
      "style": {
        "navigationBarTitleText": "隐私政策"
      }
    },
    {
      "path": "pages/login/register"
    },
    {
      "path": "pages/base/search/index"
    },
    {
      "path": "pages/goods/goods-category/index"
    },
    {
      "path": "pages/goods/goods-list/index"
    },
    {
      "path": "pages/goods/goods-detail/index"
    },
    {
      "path": "pages/shopping-cart/index"
    },
    {
      "path": "pages/order/order-confirm/index"
    },
    {
      "path": "pages/order/order-list/index",
      "style": {
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/order/order-detail/index"
    },
    {
      "path": "pages/order/order-logistics/index"
    },
    {
      "path": "pages/order/order-pay-result/index"
    },
    {
      "path": "pages/user/user-center/index"
    },
    {
      "path": "pages/user/user-info/index"
    },
    {
      "path": "pages/user/user-address/list/index"
    },
    {
      "path": "pages/user/user-address/form/index"
    },
    {
      "path": "pages/user/user-collect/index"
    },
    {
      "path": "pages/user/user-footprint/index"
    },
    {
      "path": "pages/appraises/form/index"
    },
    {
      "path": "pages/appraises/list/index"
    },
    {
      "path": "pages/order/order-refunds/form/index"
    },
    {
      "path": "pages/order/order-refunds/submit/index"
    },
    {
      "path": "pages/user/user-points-record/index"
    },
    {
      "path": "pages/coupon/coupon-user-list/index",
      "style": {
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/seckill/seckill-list/index"
    },
    {
      "path": "pages/seckill/seckill-detail/index"
    },
    {
      "path": "pages/seckill/seckill-order-confirm/index"
    },
    {
      "path": "pages/coupon/coupon-list/index",
      "style": {
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/bargain/bargain-list/index",
      "style": {
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/bargain/bargain-detail/index"
    },
    {
      "path": "pages/bargain/bargain-user-list/index",
      "style": {
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/bargain/bargain-order-confirm/index"
    },
    {
      "path": "pages/groupon/groupon-list/index",
      "style": {
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/groupon/groupon-detail/index"
    },
    {
      "path": "pages/groupon/groupon-user-list/index",
      "style": {
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/groupon/groupon-user-detail/index"
    },
    {
      "path": "pages/groupon/groupon-order-confirm/index"
    },
    {
      "path": "pages/live/room-list/index"
    },
    {
      "path": "pages/shop/shop-detail/index"
    },
    {
      "path": "pages/shop/shop-list/index"
    },
    {
      "path": "pages/message/list/index"
    },
    {
      "path": "pages/message/chat/index"
    },
    {
      "path": "pages/message/wx-chat/index"
    },
    {
      "path": "pages/customer-service/customer-service-list/index"
    },
    {
      "path": "pages/customer-service/wx-customer-service-list/index"
    },
    {
      "path": "pages/public/webview/webview"
    },
    {
      "path": "pages/article/article-list/index"
    },
    {
      "path": "pages/article/article-info/index"
    },
    {
      "path": "pages/signrecord/signrecord-info/index"
    },
    {
      "path": "pages/signrecord/signrecord-list/index"
    },
    {
      "path": "pages/distribution/distribution-center/index"
    },
    {
      "path": "pages/distribution/distribution-withdraw/index"
    },
    {
      "path": "pages/distribution/distribution-card/index"
    },
    {
      "path": "pages/distribution/distribution-withdraw-list/index"
    },
    {
      "path": "pages/distribution/distribution-withdraw-detail/index"
    },
    {
      "path": "pages/distribution/distribution-promotion-statistical/index"
    },
    {
      "path": "pages/distribution/distribution-order-list/index"
    },
    {
      "path": "pages/distribution/distribution-promotion-ranking/index"
    },
    {
      "path": "pages/order/confirm",
      "style": {
        "navigationBarTitleText": "订单确认"
      }
    }
  ],
  "globalStyle": {
  },
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#e64341",
    "backgroundColor": "#FFFFFF",
    "borderStyle": "black",
    "paddingTop": "10px",
    "list": [
      {
        "pagePath": "pages/home/<USER>",
        "text": "活动",
        "iconPath": "static/public/img/icon-1/1-001.png",
        "selectedIconPath": "static/public/img/icon-1/1-002.png"
      },
      /*{
          "pagePath": "pages/tabbar/products/index",
          "text": "全部产品",
          "iconPath": "static/public/img/icon-1/2-001.png",
          "selectedIconPath": "static/public/img/icon-1/2-002.png"
      },
      {
          "pagePath": "pages/tabbar/coupons/index",
          "text": "优惠券",
          "iconPath": "；static/public/img/icon-1/3-001.png",
          "selectedIconPath": "static/public/img/icon-1/3-002.png"
      },*/
      {
        "pagePath": "pages/order/order-list/index",
        "text": "订单",
        "iconPath": "static/public/img/icon-1/5-001.png",
        "selectedIconPath": "static/public/img/icon-1/5-002.png"
      }
    ]
  },
  "condition": {
    //模式配置，仅开发期间生效
    "current": 0,
    //当前激活的模式(list 的索引项)
    "list": [
      {
        "name": "",
        //模式名称
        "path": "",
        //启动页面，必选
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      }
    ]
  },
  "subPackages": [
  ]
}
