<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<!-- 使用卡片布局组件，设置浅灰绿色背景 -->
	<card-layout>
		<template #content>
			<!-- 页面标题 -->
			<view class="page-title">
				<text class="title-text">退款信息</text>
			</view>

			<!-- 商品信息卡片 -->
			<view class="product-card">
				<view class="product-info">
					<image :src="orderItem.picUrl ? orderItem.picUrl : '/static/public/img/no_pic.png'" mode="aspectFill"
						class="product-image">
					</image>
					<view class="product-details">
						<view class="product-title">{{ orderItem.spuName }}</view>
						<view class="product-subtitle" v-if="orderItem.specInfo">{{ orderItem.specInfo }}</view>
						<view class="purchase-time">购买时间 {{ orderItem.createTime }}</view>
					</view>
				</view>
			</view>

			<!-- 退款信息卡片 -->
			<view class="refund-card" v-for="(item, index) in orderRefundsList" :key="index">
				<view class="refund-item">
					<text class="refund-label" style="color: #fff;">发起时间：</text>
					<text class="refund-value" style="color: #fff;">{{ item.createTime }}</text>
				</view>
				<view class="refund-item">
					<text class="refund-label">退款状态：</text>
					<text class="refund-value">{{ item.statusDesc.replace('同意','已') }}</text>
				</view>
				<view class="refund-item">
					<text class="refund-label">退款金额：</text>
					<text class="refund-value">¥ {{ item.refundAmount }}</text>
				</view>
				<view class="refund-item" v-if="orderItem.paymentPoints">
					<text class="refund-label">退款积分：</text>
					<text class="refund-value">{{ orderItem.paymentPoints }}</text>
				</view>
				<view class="refund-item">
					<text class="refund-label">退款数量：</text>
					<text class="refund-value">X{{ orderItem.quantity }}</text>
				</view>
				<view class="refund-item" v-if="index == 0">
					<text class="refund-label">是否到账：</text>
					<text class="refund-value">{{ orderItem.isRefund == '1' ? '是' : '否' }}</text>
				</view>
				<view class="refund-item" v-if="item.refuseRefundReson">
					<text class="refund-label">拒绝原因：</text>
					<text class="refund-value">{{ item.refuseRefundReson }}</text>
				</view>
				<view class="refund-reason">
					<view class="reason-title">退款原因</view>
					<view class="reason-text">{{ item.refundReson }}</view>
				</view>
			</view>

			<!-- 底部联系客服按钮 -->
			<view class="bottom-action">
				<button class="confirm-btn" @click="openCustomerService">联系客服</button>
			</view>

			<!-- 客服弹窗 -->
			<customer-service-modal
				:visible="showCustomerServiceModal"
				@close="closeCustomerServiceModal"
			/>
		</template>
	</card-layout>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	const util = require("utils/util.js");
	import api from 'utils/api'
	// 引入卡片布局组件
	import CardLayout from '@/components/UI/common/card-layout/index.vue'
	// 引入客服弹窗组件
	import CustomerServiceModal from '@/components/customer-service-modal/index.vue'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				orderItem: {
					quantity: 0,
				},
				orderRefundsList: [],
				showCustomerServiceModal: false // 控制客服弹窗显示
			};
		},

		components: {
			CardLayout,
			CustomerServiceModal
		},
		props: {},

		onShow() {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			this.orderItemId = options.orderItemId;
			app.initPage().then(res => {
				this.orderItemGet(this.orderItemId);
			});
		},

		methods: {
			orderItemGet(id) {
				let that = this;
				api.orderItemGet(id).then(res => {
					let orderItem = res.data;
					this.orderItem = orderItem;
					this.orderRefundsList = orderItem.listOrderRefunds;
				});
			},

			/**
			 * 打开客服弹窗
			 */
			openCustomerService() {
				this.showCustomerServiceModal = true;
			},

			/**
			 * 关闭客服弹窗
			 */
			closeCustomerServiceModal() {
				this.showCustomerServiceModal = false;
			}

		}
	};
</script>
<style scoped>
/* 页面标题 */
.page-title {
	padding: 40rpx 40rpx 30rpx 40rpx;
}

.title-text {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

/* 商品信息卡片 */
.product-card {
	margin: 0 30rpx 30rpx 30rpx;
	background: #b4c0bd;
	border-radius: 20rpx;
	padding: 30rpx;
}

.product-info {
	display: flex;
	align-items: flex-start;
}

.product-image {
	width: 160rpx;
	height: 160rpx;
	border-radius: 15rpx;
	margin-right: 30rpx;
	background: #fff;
}

.product-details {
	flex: 1;
}

.product-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #fff;
	margin-bottom: 10rpx;
}

.product-subtitle {
	font-size: 28rpx;
	color: #fff;
	margin-bottom: 20rpx;
}

.purchase-time {
	font-size: 24rpx;
	color: #fff;
	opacity: 0.8;
}

/* 退款信息卡片 */
.refund-card {
	margin: 0 30rpx 30rpx 30rpx;
	background: #b4c0bd;
	border-radius: 20rpx;
	padding: 30rpx;
}

.refund-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	/* 移除底部线条 */
}

.refund-item:last-of-type {
	border-bottom: none;
}

.refund-label {
	font-size: 28rpx;
	color: #000; /* 改为黑色 */
}

.refund-value {
	font-size: 28rpx;
	color: #000; /* 改为黑色 */
	font-weight: bold;
}

/* 退款原因 */
.refund-reason {
	margin-top: 30rpx;
	padding-top: 30rpx;
	border-top: 1rpx solid rgba(255, 255, 255, 0.3);
}

.reason-title {
	font-size: 28rpx;
	color: #000; /* 改为黑色 */
	margin-bottom: 20rpx;
}

.reason-text {
	font-size: 26rpx;
	color: #000; /* 改为黑色 */
	line-height: 1.5;
}

/* 底部联系客服按钮 */
.bottom-action {
	position: fixed;
	bottom: 60rpx;
	right: 60rpx;
	z-index: 100;
}

.confirm-btn {
	background: #b4c0bd;
	color: black;
	border-radius: 20rpx;
	letter-spacing: 2rpx;
	font-size: 28rpx;
	border: none;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.confirm-btn:active {
	background: #6d8179;
}
</style>
