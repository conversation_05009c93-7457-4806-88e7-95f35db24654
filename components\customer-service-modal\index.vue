<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
  <!-- 客服图片弹窗 -->
  <view v-if="visible" class="customer-service-modal" @click="handleClose">
    <view class="modal-content" @click.stop>
      <view class="modal-header">
        <text class="modal-title">客服联系方式</text>
        <text class="close-btn" @click="handleClose">×</text>
      </view>
      <view class="modal-body">
        <image
          :src="imageUrl"
          mode="aspectFit"
          class="customer-service-image"
          show-menu-by-longpress="true"
          @error="onImageError"
        />
        <view class="tip-text">
          <text>长按图片可保存到相册</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
export default {
  name: 'CustomerServiceModal',
  props: {
    // 控制弹窗显示
    visible: {
      type: Boolean,
      default: false
    },
    // 客服图片URL
    imageUrl: {
      type: String,
      default: 'https://createone.oss-cn-hangzhou.aliyuncs.com/1468808703988994048/material/fd59d95b-7c0b-4c2e-9bbc-08c36c9c296f.jpg'
    }
  },
  methods: {
    /**
     * 关闭弹窗
     */
    handleClose() {
      this.$emit('close');
    },

    /**
     * 图片加载失败处理
     */
    onImageError() {
      uni.showToast({
        title: '图片加载失败',
        icon: 'none',
        duration: 2000
      });
    }
  }
};
</script>

<style scoped>
/* 客服弹窗样式 */
.customer-service-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-content {
  background-color: #ffffff;
  border-radius: 20rpx;
  width: 80%;
  max-width: 600rpx;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.close-btn {
  font-size: 40rpx;
  color: #999999;
  cursor: pointer;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-body {
  padding: 30rpx;
  text-align: center;
}

.customer-service-image {
  width: 100%;
  max-height: 800rpx;
  border-radius: 10rpx;
}

.tip-text {
  margin-top: 20rpx;
  color: #666666;
  font-size: 24rpx;
}
</style>