<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<!-- 使用卡片布局组件，设置浅灰绿色背景 -->
	<card-layout>
		<template #content>
			<!-- 页面标题 -->
			<view class="page-title">
				<text class="title-text">退款信息</text>
			</view>

			<!-- 商品信息卡片 -->
			<view class="product-card">
				<view class="product-info">
					<image :src="orderItem.picUrl ? orderItem.picUrl : ''" mode="aspectFill" class="product-image">
					</image>
					<view class="product-details">
						<view class="product-title">{{ orderItem.spuName }}</view>
						<view class="product-subtitle" v-if="orderItem.specInfo">{{ orderItem.specInfo }}</view>
						<view class="purchase-time">购买时间 {{ formatTime(orderItem.createTime) }}</view>
					</view>
				</view>
			</view>

			<!-- 退款信息卡片 -->
			<view class="refund-card">
				<view class="refund-item">
					<text class="refund-label">退款金额：</text>
					<text class="refund-value">¥ {{ orderItem.paymentPrice }}</text>
				</view>
				<view class="refund-item" v-if="orderItem.paymentPoints">
					<text class="refund-label">退款积分：</text>
					<text class="refund-value">{{ orderItem.paymentPoints }}</text>
				</view>
				<view class="refund-item">
					<text class="refund-label">退款数量：</text>
					<text class="refund-value">X{{ orderItem.quantity }}</text>
				</view>

				<!-- 退款原因 -->
				<view class="refund-reason">
					<view class="reason-title">退款原因</view>
					<textarea class="reason-input" maxlength="40" @input="resonInput" placeholder="请输入退款原因"
						:value="orderRefunds.refundReson"></textarea>
				</view>
			</view>

			<!-- 底部确认按钮 -->
			<view class="bottom-action">
				<button class="confirm-btn" @tap="subRefunds">确认并提交</button>
			</view>
		</template>
	</card-layout>
</template>

<script>
/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
const app = getApp();
const util = require("utils/util.js");
import api from 'utils/api'
// 引入卡片布局组件
import CardLayout from '@/components/UI/common/card-layout/index.vue'

export default {
	data() {
		return {
			CustomBar: this.CustomBar,
			theme: app.globalData.theme, //全局颜色变量
			orderItem: {},
			orderRefunds: {},
			orderItemId: ""
		};
	},

	components: {
		CardLayout
	},
	props: {},

	onShow() { },

	onLoad(options) {
		// 保存别人分享来的 userCode
		util.saveSharerUserCode(options);
		this.orderItemId = options.orderItemId;
		this.orderRefunds.orderItemId = options.orderItemId;
		app.initPage().then(res => {
			this.orderItemGet(this.orderItemId);
		});
	},

	methods: {
		// 格式化时间
		formatTime(time) {
			if (!time) return '2025-07-25-12:45:25';
			const date = new Date(time);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day}-${hours}:${minutes}:${seconds}`;
		},

		openCustomerService() {
			wx.openCustomerServiceChat({
				extInfo: { url: 'https://work.weixin.qq.com/kfid/kfcfd2e702b9ffd3ba7' },
				corpId: 'wwd396b79dd20220ba',
				success(res) {
					console.log(res);
				},
				fail(err) {
					console.log(err);
				},
			})
		},

		orderItemGet(id) {
			let that = this;
			api.orderItemGet(id).then(res => {
				this.orderItem = res.data;
			});
		},

		resonInput(e) {
			this.orderRefunds.refundReson = e.detail.value
		},

		radioChange(e) {
			this.orderRefunds.status = e.detail.value;
		},

		subRefunds() {
			/* if (!this.orderRefunds.status) {
				uni.showToast({
					title: '请选择退款类型',
					icon: 'none',
					duration: 2000
				});
				return;
			} */

			/* if (!this.orderRefunds.refundReson) {
				uni.showToast({
					title: '请输入退款原因',
					icon: 'none',
					duration: 2000
				});
				return;
			} */
			this.orderRefunds.status = '1';
			let that = this;
			uni.showModal({
				content: '确认提交退款申请吗？',
				cancelText: '我再想想',
				confirmColor: '#ff0000',

				success(res) {
					if (res.confirm) {
						api.orderRefundsSave(that.orderRefunds).then(res => {
							uni.redirectTo({
								url: '/pages/order/order-refunds/form/index?orderItemId=' + that.orderItemId
							});
						});
					}
				}

			});
		}

	}
};
</script>
<style scoped>
/* 页面标题 */
.page-title {
	padding: 40rpx 40rpx 30rpx 40rpx;
}

.title-text {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

/* 商品信息卡片 */
.product-card {
	margin: 0 30rpx 30rpx 30rpx;
	background: #b4c0bd;
	border-radius: 20rpx;
	padding: 30rpx;
}

.product-info {
	display: flex;
	align-items: flex-start;
}

.product-image {
	width: 160rpx;
	height: 160rpx;
	border-radius: 15rpx;
	margin-right: 30rpx;
	background: #fff;
}

.product-details {
	flex: 1;
}

.product-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #fff;
	margin-bottom: 10rpx;
}

.product-subtitle {
	font-size: 28rpx;
	color: #fff;
	margin-bottom: 20rpx;
}

.purchase-time {
	font-size: 24rpx;
	color: #fff;
	opacity: 0.8;
}

/* 退款信息卡片 */
.refund-card {
	margin: 0 30rpx 100rpx 30rpx;
	background: #b4c0bd;
	border-radius: 20rpx;
	padding: 30rpx;
}

.refund-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	/* 移除底部线条 */
}

.refund-item:last-of-type {
	border-bottom: none;
}

.refund-label {
	font-size: 28rpx;
	color: #000; /* 改为黑色 */
}

.refund-value {
	font-size: 28rpx;
	color: #000; /* 改为黑色 */
	font-weight: bold;
}

/* 退款原因 */
.refund-reason {
	margin-top: 30rpx;
	padding-top: 30rpx;
	border-top: 1rpx solid rgba(255, 255, 255, 0.3);
}

.reason-title {
	font-size: 28rpx;
	color: #000; /* 改为黑色 */
	margin-bottom: 20rpx;
}

.reason-input {
	width: 100%;
	min-height: 120rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 26rpx;
	color: #000; /* 改为黑色 */
	border: none;
}

.reason-input::placeholder {
	color: #dadcdc; /* 修改placeholder颜色为#dadcdc */
}

/* 底部确认按钮 */
.bottom-action {
	position: fixed;
	bottom: 60rpx;
	right: 60rpx;
	z-index: 100;
}

.confirm-btn {
	background: #b4c0bd;
	color: black;
	border-radius: 20rpx;
	letter-spacing: 2rpx;
	font-size: 28rpx;
	border: none;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.confirm-btn:active {
	background: #6d8179;
}
</style>
